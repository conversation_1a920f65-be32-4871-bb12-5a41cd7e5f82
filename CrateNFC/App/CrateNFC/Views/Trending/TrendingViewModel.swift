import CrateServices
import Factory
import SwiftData
import SwiftUI

public enum TrendingViewState {
  case loading
  case error(String)
  case empty
  case content([Content])
}

@MainActor
public final class TrendingViewModel: ObservableObject {
  @Published public private(set) var state: TrendingViewState = .empty

  private let apiService: ApiServiceProtocol
  private let contentService: ContentServiceProtocol

  public init() {
    self.apiService = Container.shared.apiService.resolve()
    self.contentService = Container.shared.contentService.resolve()
  }

  public func loadCachedContent() throws {
    if case .content = state {
      return
    }
    updateState(
      try contentService.getAllTrendingContent(
        context: Container.shared.modelContext.resolve()))
  }

  private func updateState(_ records: [Content]) {
    if records.isEmpty {
      state = .empty
    } else {
      state = .content(records)
    }
  }

  public func fetchTrendingContent() {
    Task { @MainActor in
      do {
        self.state = .loading

        let content = try await contentService.getTrending()
        let models = content.map { dto in
          Content(
            serverId: dto.serverId,
            detail: dto.detail,
            title: dto.title,
            mediaUrl: dto.mediaUrl,
            url: dto.url,
            updatedAt: dto.updatedAt ?? Date.distantPast,
            createdAt: dto.createdAt ?? Date.distantPast
          )
        }

        // Save the fetched content to SwiftData
        try storeFetchedContent(models)

        self.state = .content(models)

        print("Successfully fetched and saved \(content.count) trending content items")
      } catch {
        print("Error fetching trending content: \(error.localizedDescription)")

        do {
          let cachedContent = try contentService.getAllTrendingContent(
            context: Container.shared.modelContext.resolve())
          if !cachedContent.isEmpty {
            self.state = .content(cachedContent)
            return
          }
        } catch {
        }

        self.state = .error(error.localizedDescription)
      }
    }
  }

  func storeFetchedContent(_ fetchedContent: [Content]) throws {
    try contentService.deleteAllTrendingContent(
      context: Container.shared.modelContext.resolve())
    try contentService.saveTrendingContent(
      fetchedContent, context: Container.shared.modelContext.resolve())
  }
}
